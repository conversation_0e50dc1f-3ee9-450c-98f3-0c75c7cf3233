SQL Pair Programming

Please review and be familiar with the below schema in advance of the interview.

CREATE TABLE `Person` (
  `PersonId` int unsigned NOT NULL AUTO_INCREMENT,
  `LastName` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
DEFAULT NULL,
  `FirstName` varchar(35) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
DEFAULT NULL,
  PRIMARY KEY (`PersonId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `Invoice` (
                           `InvoiceId` int unsigned NOT NULL AUTO_INCREMENT,
                           `PersonId` int unsigned NOT NULL,
                           PRIMARY KEY (`InvoiceId`),
                           KEY `invoice_personid_foreign` (`PersonId`),
                           CONSTRAINT `invoice_personid_foreign` FOREIG<PERSON> KEY (`PersonId`) REFERENCES
                               `Person` (`PersonId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `InvoiceItem` (
                               `InvoiceItemId` int unsigned NOT NULL AUTO_INCREMENT,
                               `InvoiceId` int unsigned NOT NULL,
                               `Amount` decimal(11,2) DEFAULT NULL,
                               PRIMARY KEY (`InvoiceItemId`),
                               KEY `invoiceitem_invoiceid_foreign` (`InvoiceId`),
                               CONSTRAINT `invoiceitem_invoiceid_foreign` FOREIGN KEY (`InvoiceId`)
                                   REFERENCES `Invoice` (`InvoiceId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `Payment` (
                           `PaymentId` int unsigned NOT NULL AUTO_INCREMENT,
                           `PersonId` int unsigned NOT NULL,
                           `PaymentMethod` char(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
                                                         DEFAULT NULL,
                           `PaymentAmount` decimal(11,2) DEFAULT NULL,
                           PRIMARY KEY (`PaymentId`),
                           KEY `payment_personid_foreign` (`PersonId`),
                           CONSTRAINT `payment_personid_foreign` FOREIGN KEY (`PersonId`) REFERENCES
                               `Person` (`PersonId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `InvoicePayment` (
                                  `InvoicePaymentId` int unsigned NOT NULL AUTO_INCREMENT,
                                  `PaymentId` int unsigned NOT NULL,
                                  `InvoiceId` int unsigned DEFAULT NULL,
                                  `Amount` decimal(11,2) DEFAULT NULL,
                                  PRIMARY KEY (`InvoicePaymentId`),
                                  KEY `cashitem_paymentid_foreign` (`PaymentId`),
                                  KEY `cashitem_invoiceid_foreign` (`InvoiceId`),
                                  CONSTRAINT `cashitem_paymentid_foreign` FOREIGN KEY (`PaymentId`)
                                      REFERENCES `Payment` (`PaymentId`),
                                  CONSTRAINT `cashitem_invoiceid_foreign` FOREIGN KEY (`InvoiceId`)
                                      REFERENCES `Invoice` (`InvoiceId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
